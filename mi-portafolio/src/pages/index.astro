---

import Layout from '../layouts/Layout.astro';
import Hero from '../components/Hero.astro';
import Habilidades from '../components/Habilidades.astro';
import Experiencia from '../components/Experiencia.astro';
import Logros from '../components/Logros.astro';
import Proyectos from '../components/Proyectos.astro';
import Testimonios from '../components/Testimonios.astro';
import Estudios from '../components/Estudios.astro';
import Contacto from '../components/Contacto.astro';

---

<!-- Esta es la parte del "template", lo que realmente se convierte en HTML. -->

<!-- 
  Usamos nuestro componente Layout como un "envoltorio" para toda la página.
  Le pasamos la propiedad "title" que definimos en Layout.astro.
  Todo lo que pongamos entre <Layout> y </Layout> se insertará en el <slot /> del layout.
-->
<Layout title="<PERSON> | Desarrollador Backend & Embebidos">
	
  <!--
    Ahora simplemente colocamos nuestros componentes importados en el orden que queramos.
    Astro los renderizará uno después del otro.
    ¡Es como apilar bloques de Lego!
  -->
  <Hero />
  <Experiencia />
  <Habilidades />
  <Proyectos />
  <Logros />
  <Testimonios />
  <Estudios />
  <Contacto />

</Layout>
