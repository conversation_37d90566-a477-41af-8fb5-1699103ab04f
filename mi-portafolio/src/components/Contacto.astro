<!-- SECTION: Contact -->
<section id="contacto" class="py-12 sm:py-16 px-4 sm:px-0">
    <!-- Availability Status -->
    <div class="text-center mb-8 sm:mb-12">
        <div class="inline-flex items-center gap-2 bg-green-900/30 border border-green-700/50 rounded-full px-4 py-2 mb-4">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-green-400 text-sm font-medium">Disponible para nuevos proyectos</span>
        </div>
        <h2 class="text-2xl sm:text-3xl font-bold text-white">¿Listo para trabajar juntos?</h2>
        <p class="mt-4 max-w-2xl mx-auto text-slate-400 text-sm sm:text-base leading-relaxed">
            Estoy buscando oportunidades donde pueda aplicar mi experiencia en desarrollo full-stack y sistemas embebidos.
            Si tienes un proyecto desafiante o una posición que requiera soluciones innovadoras, hablemos.
        </p>
    </div>

    <!-- Contact Options -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 max-w-4xl mx-auto mb-8">
        <!-- Email -->
        <a href="mailto:<EMAIL>" class="group bg-slate-800 hover:bg-slate-700 border border-slate-700 hover:border-sky-500 rounded-lg p-4 sm:p-6 transition-all duration-300">
            <div class="text-center">
                <div class="w-12 h-12 bg-sky-500/20 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-sky-500/30 transition-colors">
                    <svg class="w-6 h-6 text-sky-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-base sm:text-lg font-semibold text-white mb-2">Email</h3>
                <p class="text-xs sm:text-sm text-slate-400"><EMAIL></p>
                <p class="text-xs text-sky-400 mt-2">Respuesta en 24h</p>
            </div>
        </a>

        <!-- LinkedIn -->
        <a href="https://linkedin.com/in/tomjod" target="_blank" class="group bg-slate-800 hover:bg-slate-700 border border-slate-700 hover:border-blue-500 rounded-lg p-4 sm:p-6 transition-all duration-300">
            <div class="text-center">
                <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-500/30 transition-colors">
                    <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                </div>
                <h3 class="text-base sm:text-lg font-semibold text-white mb-2">LinkedIn</h3>
                <p class="text-xs sm:text-sm text-slate-400">Conectemos profesionalmente</p>
                <p class="text-xs text-blue-400 mt-2">Perfil completo</p>
            </div>
        </a>

        <!-- GitHub -->
        <a href="https://github.com/tomjod" target="_blank" class="group bg-slate-800 hover:bg-slate-700 border border-slate-700 hover:border-purple-500 rounded-lg p-4 sm:p-6 transition-all duration-300">
            <div class="text-center">
                <div class="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-500/30 transition-colors">
                    <svg class="w-6 h-6 text-purple-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                </div>
                <h3 class="text-base sm:text-lg font-semibold text-white mb-2">GitHub</h3>
                <p class="text-xs sm:text-sm text-slate-400">Revisa mi código</p>
                <p class="text-xs text-purple-400 mt-2">Proyectos activos</p>
            </div>
        </a>
    </div>

    <!-- Quick Stats -->
    <div class="text-center">
        <div class="inline-flex items-center gap-6 sm:gap-8 text-xs sm:text-sm text-slate-400">
            <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Tiempo de respuesta: 24h</span>
            </div>
            <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-sky-400 rounded-full"></div>
                <span>Zona horaria: GMT-4</span>
            </div>
            <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span>Modalidad: Remoto/Híbrido</span>
            </div>
        </div>
    </div>
</section>