<!-- SECTION: Achievements & Impact -->
<section id="logros" class="py-12 sm:py-16">
    <h2 class="text-2xl sm:text-3xl font-bold text-white mb-6 sm:mb-8 text-center md:text-left">Logros & Impacto</h2>
    
    <!-- Major Achievements -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-8 sm:mb-12">
        <!-- Achievement 1 -->
        <div class="bg-gradient-to-r from-sky-900/20 to-blue-900/20 border border-sky-700/30 rounded-lg p-4 sm:p-6">
            <div class="flex items-start gap-4">
                <div class="bg-sky-500 rounded-full p-2 sm:p-3 flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-base sm:text-lg font-bold text-white mb-2">Modernización Completa de Sistema</h3>
                    <p class="text-xs sm:text-sm text-slate-400 mb-3">Migré exitosamente un sistema legacy de Windows Forms a Android nativo, reduciendo el tiempo de procesamiento en un 60% y mejorando la experiencia del usuario.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="text-xs bg-sky-900/50 text-sky-300 px-2 py-1 rounded">Legacy Migration</span>
                        <span class="text-xs bg-sky-900/50 text-sky-300 px-2 py-1 rounded">Performance +60%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Achievement 2 -->
        <div class="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-700/30 rounded-lg p-4 sm:p-6">
            <div class="flex items-start gap-4">
                <div class="bg-green-500 rounded-full p-2 sm:p-3 flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-base sm:text-lg font-bold text-white mb-2">Automatización con IA</h3>
                    <p class="text-xs sm:text-sm text-slate-400 mb-3">Implementé sistema de soporte técnico automatizado con RAG e IA, reduciendo tickets de soporte en 80% y mejorando tiempo de respuesta a menos de 2 minutos.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="text-xs bg-green-900/50 text-green-300 px-2 py-1 rounded">AI/RAG</span>
                        <span class="text-xs bg-green-900/50 text-green-300 px-2 py-1 rounded">Tickets -80%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Achievement 3 -->
        <div class="bg-gradient-to-r from-purple-900/20 to-violet-900/20 border border-purple-700/30 rounded-lg p-4 sm:p-6">
            <div class="flex items-start gap-4">
                <div class="bg-purple-500 rounded-full p-2 sm:p-3 flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-base sm:text-lg font-bold text-white mb-2">Hardware-Software Integration</h3>
                    <p class="text-xs sm:text-sm text-slate-400 mb-3">Diseñé y desarrollé placa de control personalizada con Arduino, integrando sensores y actuadores para control automatizado en tiempo real.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="text-xs bg-purple-900/50 text-purple-300 px-2 py-1 rounded">Custom PCB</span>
                        <span class="text-xs bg-purple-900/50 text-purple-300 px-2 py-1 rounded">Real-time Control</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Achievement 4 -->
        <div class="bg-gradient-to-r from-orange-900/20 to-red-900/20 border border-orange-700/30 rounded-lg p-4 sm:p-6">
            <div class="flex items-start gap-4">
                <div class="bg-orange-500 rounded-full p-2 sm:p-3 flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-base sm:text-lg font-bold text-white mb-2">Open Source Contribution</h3>
                    <p class="text-xs sm:text-sm text-slate-400 mb-3">Creé y publiqué buicap-py, una librería Python para integración con hardware especializado, facilitando el desarrollo para otros programadores.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="text-xs bg-orange-900/50 text-orange-300 px-2 py-1 rounded">Open Source</span>
                        <span class="text-xs bg-orange-900/50 text-orange-300 px-2 py-1 rounded">Python Library</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Problem-Solving Approach -->
    <div class="bg-slate-800 rounded-lg p-4 sm:p-6">
        <h3 class="text-lg sm:text-xl font-bold text-white mb-4 text-center">Mi Enfoque de Resolución de Problemas</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
                <div class="bg-sky-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3">
                    <span class="text-lg sm:text-2xl">🔍</span>
                </div>
                <h4 class="text-sm sm:text-base font-semibold text-white mb-2">Análisis</h4>
                <p class="text-xs sm:text-sm text-slate-400">Entiendo el problema desde la raíz</p>
            </div>
            <div class="text-center">
                <div class="bg-green-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3">
                    <span class="text-lg sm:text-2xl">💡</span>
                </div>
                <h4 class="text-sm sm:text-base font-semibold text-white mb-2">Diseño</h4>
                <p class="text-xs sm:text-sm text-slate-400">Arquitectura escalable y mantenible</p>
            </div>
            <div class="text-center">
                <div class="bg-purple-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3">
                    <span class="text-lg sm:text-2xl">⚙️</span>
                </div>
                <h4 class="text-sm sm:text-base font-semibold text-white mb-2">Implementación</h4>
                <p class="text-xs sm:text-sm text-slate-400">Código limpio y eficiente</p>
            </div>
            <div class="text-center">
                <div class="bg-orange-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3">
                    <span class="text-lg sm:text-2xl">🚀</span>
                </div>
                <h4 class="text-sm sm:text-base font-semibold text-white mb-2">Optimización</h4>
                <p class="text-xs sm:text-sm text-slate-400">Mejora continua del rendimiento</p>
            </div>
        </div>
    </div>
</section>
